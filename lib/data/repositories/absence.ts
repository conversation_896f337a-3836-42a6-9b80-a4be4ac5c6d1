import { eq, and, gte, lte } from 'drizzle-orm'
import { db, schema } from '../drizzle/db'
import {
  Absence,
  AttendanceType,
  AttendanceSummary,
  CreateAbsenceDTO,
} from '../../domain/entities/absence'

// Interface for updating an absence
interface UpdateAbsenceDTO {
  recordedAt: Date
}

/**
 * Absence repository implementation
 */
export class AbsenceRepository {
  /**
   * Find an absence by unique code, type, and date
   */
  async findByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<Absence | null> {
    // Ensure we're working with a valid date
    if (!date || isNaN(date.getTime())) {
      console.error('Invalid date provided to findByUniqueCodeAndTypeAndDate:', date)
      return null
    }

    // Create a new date object to avoid modifying the original
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    // Convert dates to ISO strings for SQL
    const startDateStr = startOfDay.toISOString()
    const endDateStr = endOfDay.toISOString()

    const [absence] = await db
      .select()
      .from(schema.absences)
      .where(
        and(
          eq(schema.absences.uniqueCode, uniqueCode),
          eq(schema.absences.type, type),
          gte(schema.absences.recordedAt, new Date(startDateStr)),
          lte(schema.absences.recordedAt, new Date(endDateStr))
        )
      )
      .limit(1)

    if (!absence) {
      return null
    }

    return this.mapToAbsence(absence)
  }

  /**
   * Create a new absence record
   */
  async create(data: CreateAbsenceDTO): Promise<Absence> {
    const [absence] = await db
      .insert(schema.absences)
      .values({
        uniqueCode: data.uniqueCode,
        type: data.type,
        recordedAt: data.recordedAt,
        createdAt: new Date(),
      })
      .returning()

    return this.mapToAbsence(absence)
  }

  /**
   * Get attendance summary for reporting
   */
  async getAttendanceSummary(
    date?: Date,
    className?: string,
    isWeekFilter?: boolean
  ): Promise<AttendanceSummary[]> {
    try {
      // Set default date to today if not provided
      if (!date) {
        date = new Date()
      }

      // Create date range based on filter type
      let startOfDay: Date
      let endOfDay: Date

      if (isWeekFilter) {
        // For week filter, get data for the past week (including today)
        // Use WITA timezone for consistent date handling

        // Get current time in WITA timezone
        const witaTime = new Intl.DateTimeFormat('en-CA', {
          timeZone: 'Asia/Makassar',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        }).formatToParts(date || new Date())

        const year = parseInt(witaTime.find(part => part.type === 'year')?.value || '0')
        const month = parseInt(witaTime.find(part => part.type === 'month')?.value || '1') - 1
        const day = parseInt(witaTime.find(part => part.type === 'day')?.value || '1')

        // Create clean today date in WITA
        const cleanToday = new Date(year, month, day)

        // Create WITA range first
        const witaEndOfDay = new Date(cleanToday)
        witaEndOfDay.setHours(23, 59, 59, 999)

        const witaStartOfDay = new Date(cleanToday)
        witaStartOfDay.setDate(witaStartOfDay.getDate() - 6)
        witaStartOfDay.setHours(0, 0, 0, 0)

        // Convert WITA range to UTC for database query
        // WITA = UTC+8, so to convert WITA to UTC: subtract 8 hours
        endOfDay = new Date(witaEndOfDay.getTime() - 8 * 60 * 60 * 1000)
        startOfDay = new Date(witaStartOfDay.getTime() - 8 * 60 * 60 * 1000)

        console.log('WITA range:', {
          start: witaStartOfDay.toISOString(),
          end: witaEndOfDay.toISOString(),
        })
        console.log('UTC range for database query:', {
          start: startOfDay.toISOString(),
          end: endOfDay.toISOString(),
        })

        // Debug each day in the week for clarity
        const weekDays = []
        for (let i = 0; i <= 6; i++) {
          const dayDate = new Date(cleanToday)
          dayDate.setDate(dayDate.getDate() - 6 + i)
          weekDays.push(dayDate.toISOString().split('T')[0])
        }

        console.log('Expected dates in weekly filter (WITA):', weekDays)
        console.log('Database query range (UTC):', {
          start: startOfDay.toISOString(),
          end: endOfDay.toISOString(),
        })
      } else {
        // For day filter, get data for the specific day
        // Create WITA range first
        const witaStartOfDay = new Date(date)
        witaStartOfDay.setHours(0, 0, 0, 0)

        const witaEndOfDay = new Date(date)
        witaEndOfDay.setHours(23, 59, 59, 999)

        // Convert WITA range to UTC for database query
        // WITA = UTC+8, so to convert WITA to UTC: subtract 8 hours
        startOfDay = new Date(witaStartOfDay.getTime() - 8 * 60 * 60 * 1000)
        endOfDay = new Date(witaEndOfDay.getTime() - 8 * 60 * 60 * 1000)

        console.log('Daily filter - WITA range:', {
          start: witaStartOfDay.toISOString(),
          end: witaEndOfDay.toISOString(),
        })
        console.log('Daily filter - UTC range for database query:', {
          start: startOfDay.toISOString(),
          end: endOfDay.toISOString(),
        })
      }

      // Format dates as ISO strings for SQL
      const startDateStr = startOfDay.toISOString()
      const endDateStr = endOfDay.toISOString()

      // Build the query conditions
      const conditions = [eq(schema.users.role, 'student')]

      // Add class filter if provided
      if (className) {
        conditions.push(eq(schema.classes.name, className))
      }

      // Get all students who have attendance records for the day
      const students = await db
        .select({
          uniqueCode: schema.users.uniqueCode,
          name: schema.users.name,
          className: schema.classes.name,
        })
        .from(schema.users)
        .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
        .innerJoin(
          schema.absences,
          and(
            eq(schema.absences.uniqueCode, schema.users.uniqueCode),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )
        .where(and(...conditions))
        .groupBy(schema.users.uniqueCode, schema.users.name, schema.classes.name)

      // For each student, get their attendance records for the day
      const summaries: AttendanceSummary[] = []

      for (const student of students) {
        // Skip if uniqueCode is null (should never happen, but just to be safe)
        if (!student.uniqueCode) continue

        // Get all attendance records for this student on this day
        const attendanceRecords = await db
          .select({
            type: schema.absences.type,
            recordedAt: schema.absences.recordedAt,
          })
          .from(schema.absences)
          .where(
            and(
              eq(schema.absences.uniqueCode, student.uniqueCode),
              gte(schema.absences.recordedAt, new Date(startDateStr)),
              lte(schema.absences.recordedAt, new Date(endDateStr))
            )
          )

        // Initialize attendance flags
        let zuhr = false
        let asr = false
        let dismissal = false
        let ijin = false
        let latestRecordedAt: Date | null = null

        // Check each attendance type
        for (const record of attendanceRecords) {
          if (record.type === AttendanceType.ZUHR) {
            zuhr = true
            if (!latestRecordedAt || record.recordedAt > latestRecordedAt) {
              latestRecordedAt = record.recordedAt
            }
          } else if (record.type === AttendanceType.ASR) {
            asr = true
            if (!latestRecordedAt || record.recordedAt > latestRecordedAt) {
              latestRecordedAt = record.recordedAt
            }
          } else if (record.type === AttendanceType.DISMISSAL) {
            dismissal = true
            if (!latestRecordedAt || record.recordedAt > latestRecordedAt) {
              latestRecordedAt = record.recordedAt
            }
          } else if (record.type === AttendanceType.IJIN) {
            ijin = true
            if (!latestRecordedAt || record.recordedAt > latestRecordedAt) {
              latestRecordedAt = record.recordedAt
            }
          }
        }

        // Add to summaries
        summaries.push({
          summaryDate: latestRecordedAt ? new Date(latestRecordedAt) : startOfDay,
          uniqueCode: student.uniqueCode,
          name: student.name || 'Unknown',
          className: student.className || 'Belum ditentukan',
          zuhr,
          asr,
          dismissal,
          ijin,
          updatedAt: latestRecordedAt || startOfDay,
        })

        // Debug log for the records we're adding to the summary
        console.log(`Added summary record for ${student.name}:
          uniqueCode: ${student.uniqueCode}
          summaryDate: ${(latestRecordedAt || startOfDay).toISOString()}
          updatedAt: ${(latestRecordedAt || startOfDay).toISOString()}
          zuhr: ${zuhr}, asr: ${asr}, dismissal: ${dismissal}, ijin: ${ijin}
        `)
      }

      // Add summary analysis of dates in the result
      if (isWeekFilter) {
        console.log('========== SUMMARY DATE ANALYSIS ==========')
        // Group records by date
        const dateMap = new Map()
        summaries.forEach(summary => {
          const dateKey = summary.summaryDate.toISOString().split('T')[0]
          if (!dateMap.has(dateKey)) {
            dateMap.set(dateKey, 0)
          }
          dateMap.set(dateKey, dateMap.get(dateKey) + 1)
        })

        // Report on the dates we found data for
        console.log(`Records by date: ${JSON.stringify(Object.fromEntries(dateMap), null, 2)}`)
        console.log(`Total unique dates with data: ${dateMap.size}`)
        console.log('=========================================')
      }

      return summaries
    } catch (error) {
      console.error('Error in getAttendanceSummary:', error)
      return []
    }
  }

  /**
   * Get detailed weekly attendance summary with individual day records
   */
  async getDetailedWeeklyAttendanceSummary(date?: Date, className?: string): Promise<any[]> {
    try {
      // Set default date to today if not provided
      if (!date) {
        date = new Date()
      }

      // Get current time in WITA timezone
      const witaTime = new Intl.DateTimeFormat('en-CA', {
        timeZone: 'Asia/Makassar',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      }).formatToParts(date || new Date())

      const year = parseInt(witaTime.find(part => part.type === 'year')?.value || '0')
      const month = parseInt(witaTime.find(part => part.type === 'month')?.value || '1') - 1
      const day = parseInt(witaTime.find(part => part.type === 'day')?.value || '1')

      // Create clean today date in WITA
      const cleanToday = new Date(year, month, day)

      // Generate array of the past 7 days (including today)
      const weekDays = []
      for (let i = 0; i < 7; i++) {
        const dayDate = new Date(cleanToday)
        dayDate.setDate(dayDate.getDate() - i)
        weekDays.push(dayDate)
      }

      console.log(
        'Getting detailed weekly data for dates:',
        weekDays.map(d => d.toISOString().split('T')[0])
      )

      // Get all students first
      const conditions = [eq(schema.users.role, 'student')]
      if (className) {
        conditions.push(eq(schema.classes.name, className))
      }

      const allStudents = await db
        .select({
          uniqueCode: schema.users.uniqueCode,
          name: schema.users.name,
          className: schema.classes.name,
        })
        .from(schema.users)
        .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
        .where(and(...conditions))

      console.log(`Found ${allStudents.length} students for weekly report`)

      // For each student, get their attendance for each day
      const studentWeeklyData = []

      for (const student of allStudents) {
        if (!student.uniqueCode) continue

        const weeklyRecords: any = {}
        let hasAnyAttendance = false

        // Get attendance for each day
        for (const dayDate of weekDays) {
          const dayKey = dayDate.toISOString().split('T')[0]

          // Create WITA range for this day
          const witaStartOfDay = new Date(dayDate)
          witaStartOfDay.setHours(0, 0, 0, 0)

          const witaEndOfDay = new Date(dayDate)
          witaEndOfDay.setHours(23, 59, 59, 999)

          // Convert to UTC for database query
          const utcStartOfDay = new Date(witaStartOfDay.getTime() - 8 * 60 * 60 * 1000)
          const utcEndOfDay = new Date(witaEndOfDay.getTime() - 8 * 60 * 60 * 1000)

          // Get attendance records for this student on this day
          const dayAttendance = await db
            .select({
              type: schema.absences.type,
              recordedAt: schema.absences.recordedAt,
            })
            .from(schema.absences)
            .where(
              and(
                eq(schema.absences.uniqueCode, student.uniqueCode),
                gte(schema.absences.recordedAt, utcStartOfDay),
                lte(schema.absences.recordedAt, utcEndOfDay)
              )
            )

          // Process attendance for this day
          let zuhr = false,
            asr = false,
            dismissal = false,
            ijin = false
          let zuhrTime = null,
            asrTime = null,
            dismissalTime = null,
            ijinTime = null

          for (const record of dayAttendance) {
            hasAnyAttendance = true
            const recordTime = new Date(record.recordedAt.getTime() + 8 * 60 * 60 * 1000) // Convert back to WITA
            const timeStr = recordTime.toLocaleTimeString('id-ID', {
              hour: '2-digit',
              minute: '2-digit',
              timeZone: 'Asia/Makassar',
            })

            if (record.type === AttendanceType.ZUHR) {
              zuhr = true
              zuhrTime = timeStr
            } else if (record.type === AttendanceType.ASR) {
              asr = true
              asrTime = timeStr
            } else if (record.type === AttendanceType.DISMISSAL) {
              dismissal = true
              dismissalTime = timeStr
            } else if (record.type === AttendanceType.IJIN) {
              ijin = true
              ijinTime = timeStr
            }
          }

          // Format date for display
          const formattedDate = dayDate.toLocaleDateString('id-ID', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            timeZone: 'Asia/Makassar',
          })

          weeklyRecords[dayKey] = {
            date: formattedDate,
            zuhr,
            zuhrTime,
            asr,
            asrTime,
            dismissal,
            dismissalTime,
            ijin,
            ijinTime,
            rawDate: dayDate,
            isEmpty: !zuhr && !asr && !dismissal && !ijin,
          }
        }

        // Only include students who have some attendance data
        if (hasAnyAttendance) {
          studentWeeklyData.push({
            uniqueCode: student.uniqueCode,
            name: student.name || 'Unknown',
            className: student.className || 'Belum ditentukan',
            weeklyRecords: Object.values(weeklyRecords).sort(
              (a: any, b: any) => b.rawDate.getTime() - a.rawDate.getTime()
            ),
            // Summary flags (true if any day has attendance)
            zuhr: Object.values(weeklyRecords).some((r: any) => r.zuhr),
            asr: Object.values(weeklyRecords).some((r: any) => r.asr),
            dismissal: Object.values(weeklyRecords).some((r: any) => r.dismissal),
            ijin: Object.values(weeklyRecords).some((r: any) => r.ijin),
            summaryDate: 'Minggu Ini',
          })
        }
      }

      console.log(`Returning ${studentWeeklyData.length} students with weekly attendance data`)
      return studentWeeklyData
    } catch (error) {
      console.error('Error in getDetailedWeeklyAttendanceSummary:', error)
      return []
    }
  }

  /**
   * Find all absences by unique code and date
   */
  async findByUniqueCodeAndDate(uniqueCode: string, date: Date): Promise<Absence[]> {
    try {
      // Ensure we have a valid date
      if (!date || isNaN(date.getTime())) {
        console.error('Invalid date provided to findByUniqueCodeAndDate:', date)
        return []
      }

      // Create date range for the day
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      // Format dates as ISO strings for SQL
      const startDateStr = startOfDay.toISOString()
      const endDateStr = endOfDay.toISOString()

      // Query the database
      const absences = await db
        .select()
        .from(schema.absences)
        .where(
          and(
            eq(schema.absences.uniqueCode, uniqueCode),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )

      // Map the results to domain entities
      return absences.map(absence => this.mapToAbsence(absence))
    } catch (error) {
      console.error('Error in findByUniqueCodeAndDate:', error)
      return []
    }
  }

  /**
   * Refresh the materialized view for attendance summary
   */
  async refreshMaterializedView(): Promise<void> {
    // In a real implementation, this would refresh the materialized view
    // For now, we'll just do nothing
  }

  /**
   * Update an existing absence record
   */
  async update(id: number, data: UpdateAbsenceDTO): Promise<Absence> {
    const [absence] = await db
      .update(schema.absences)
      .set({
        recordedAt: data.recordedAt,
      })
      .where(eq(schema.absences.id, id))
      .returning()

    return this.mapToAbsence(absence)
  }

  /**
   * Delete an absence record by unique code, type, and date
   */
  async deleteByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<boolean> {
    try {
      // Create date range for the day
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      // Format dates as ISO strings for SQL
      const startDateStr = startOfDay.toISOString()
      const endDateStr = endOfDay.toISOString()

      // Find the record first to confirm it exists
      const [absence] = await db
        .select()
        .from(schema.absences)
        .where(
          and(
            eq(schema.absences.uniqueCode, uniqueCode),
            eq(schema.absences.type, type),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )
        .limit(1)

      if (!absence) {
        return false // Record not found
      }

      // Delete the record
      await db
        .delete(schema.absences)
        .where(
          and(
            eq(schema.absences.uniqueCode, uniqueCode),
            eq(schema.absences.type, type),
            gte(schema.absences.recordedAt, new Date(startDateStr)),
            lte(schema.absences.recordedAt, new Date(endDateStr))
          )
        )

      return true // Record deleted successfully
    } catch (error) {
      console.error('Error in deleteByUniqueCodeAndTypeAndDate:', error)
      return false
    }
  }

  /**
   * Delete all absences for a student by unique code
   */
  async deleteAllByUniqueCode(uniqueCode: string): Promise<void> {
    try {
      // Delete all attendance records for this student
      await db.delete(schema.absences).where(eq(schema.absences.uniqueCode, uniqueCode))

      console.info(`Deleted all attendance records for student with uniqueCode: ${uniqueCode}`)
    } catch (error) {
      console.error(`Error deleting attendance records for student ${uniqueCode}:`, error)
      throw error
    }
  }

  /**
   * Map a database absence to an Absence entity
   */
  private mapToAbsence(absence: any): Absence {
    return {
      id: absence.id,
      uniqueCode: absence.uniqueCode,
      type: absence.type,
      recordedAt: absence.recordedAt,
      createdAt: absence.createdAt,
    }
  }
}
